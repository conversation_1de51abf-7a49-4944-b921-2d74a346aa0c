"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON> } from "lucide-react";
import { toast } from "sonner";
import { Veo3JsonGeneratorConfig } from "@/types/veo-3";
import JsonPreview from "./veo-3-json-preview";

interface AiModeProps {
  config: Veo3JsonGeneratorConfig;
}

export default function AiMode({ config }: AiModeProps) {
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  const handleGenerate = async () => {
    if (!input.trim()) {
      toast.error(config.errorNoInput);
      return;
    }

    setIsGenerating(true);
    
    try {
      const locale = document.documentElement.lang || "en";
      const response = await fetch("/api/generate/enhance-prompt", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          input: input.trim(),
          mode: "veo-3-json",
          locale,
          model_type: "video",
          model_name: "veo-3",
        }),
      });

      const result = await response.json();
      
      if (result.code === -1) {
        toast.error(result.message);
        return;
      }

      setOutput(result.data.text);
      toast.success(config.successGenerated);
    } catch (error) {
      console.error("Error generating JSON:", error);
      toast.error(config.errorGenerateFailed);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyOutput = async () => {
    if (!output) {
      toast.error(config.errorCopyFailed);
      return;
    }

    try {
      await navigator.clipboard.writeText(output);
      toast.success(config.successCopied);
    } catch (error) {
      toast.error(config.errorCopyFailed);
    }
  };

  const handleClear = () => {
    setInput("");
    setOutput("");
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 左侧：输入区域 */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{config.inputPrompt}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="prompt-input" className="text-card-foreground">Original Prompt</Label>
              <Textarea
                id="prompt-input"
                placeholder="Enter your original video prompt here..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="min-h-[200px] resize-none bg-input border-border text-foreground placeholder:text-muted-foreground"
              />
            </div>
            
            <div className="flex gap-2">
              <Button 
                onClick={handleGenerate}
                disabled={isGenerating || !input.trim()}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {config.generating}
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    {config.generateButton}
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={handleClear}
                disabled={isGenerating}
              >
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 右侧：输出区域 */}
      <div className="space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>{config.jsonPreview}</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyOutput}
              disabled={!output}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              {config.copyJson}
            </Button>
          </CardHeader>
          <CardContent>
            {output ? (
              <JsonPreview content={output} />
            ) : (
              <div className="flex items-center justify-center h-[300px] text-muted-foreground bg-muted/30 rounded-lg border border-border">
                <div className="text-center">
                  <Sparkles className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
                  <p className="text-muted-foreground">Generated JSON will appear here</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
