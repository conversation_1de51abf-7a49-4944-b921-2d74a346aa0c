"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, X } from "lucide-react";
import { Veo3Template, Veo3FormData, TemplateField } from "@/types/veo-3";

interface DynamicFormProps {
  template: Veo3Template;
  formData: Veo3FormData;
  onChange: (data: Veo3FormData) => void;
}

export default function DynamicForm({ template, formData, onChange }: DynamicFormProps) {
  const [localData, setLocalData] = useState<Veo3FormData>(formData);

  useEffect(() => {
    setLocalData(formData);
  }, [formData]);

  const updateValue = (path: string[], value: any) => {
    const newData = { ...localData };
    let current = newData;
    
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]) {
        current[path[i]] = {};
      }
      current = current[path[i]];
    }
    
    current[path[path.length - 1]] = value;
    setLocalData(newData);
    onChange(newData);
  };

  const getValue = (path: string[]): any => {
    let current = localData;
    for (const key of path) {
      if (current && typeof current === 'object') {
        current = current[key];
      } else {
        return undefined;
      }
    }
    return current;
  };

  const addArrayItem = (path: string[]) => {
    const currentArray = getValue(path) || [];
    updateValue(path, [...currentArray, ""]);
  };

  const removeArrayItem = (path: string[], index: number) => {
    const currentArray = getValue(path) || [];
    const newArray = currentArray.filter((_: any, i: number) => i !== index);
    updateValue(path, newArray);
  };

  const updateArrayItem = (path: string[], index: number, value: string) => {
    const currentArray = getValue(path) || [];
    const newArray = [...currentArray];
    newArray[index] = value;
    updateValue(path, newArray);
  };

  const renderField = (field: TemplateField, path: string[] = []): React.ReactNode => {
    const fieldPath = [...path, field.key];
    const value = getValue(fieldPath);

    switch (field.type) {
      case 'text':
        return (
          <div key={field.key} className="space-y-2">
            <Label htmlFor={fieldPath.join('.')} className="text-card-foreground">{field.label.en}</Label>
            <Input
              id={fieldPath.join('.')}
              value={value || ""}
              onChange={(e) => updateValue(fieldPath, e.target.value)}
              placeholder={field.placeholder?.en}
              className="bg-input border-border text-foreground placeholder:text-muted-foreground"
            />
          </div>
        );

      case 'textarea':
        return (
          <div key={field.key} className="space-y-2">
            <Label htmlFor={fieldPath.join('.')} className="text-card-foreground">{field.label.en}</Label>
            <Textarea
              id={fieldPath.join('.')}
              value={value || ""}
              onChange={(e) => updateValue(fieldPath, e.target.value)}
              placeholder={field.placeholder?.en}
              className="min-h-[100px] bg-input border-border text-foreground placeholder:text-muted-foreground"
            />
          </div>
        );

      case 'select':
        return (
          <div key={field.key} className="space-y-2">
            <Label className="text-card-foreground">{field.label.en}</Label>
            <Select value={value || ""} onValueChange={(newValue) => updateValue(fieldPath, newValue)}>
              <SelectTrigger className="bg-input border-border text-foreground">
                <SelectValue placeholder="Select an option..." />
              </SelectTrigger>
              <SelectContent className="bg-popover border-border">
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option} className="text-popover-foreground">
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'array':
        const arrayValue = value || [];
        return (
          <div key={field.key} className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-card-foreground">{field.label.en}</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem(fieldPath)}
                className="h-8 w-8 p-0 border-border hover:bg-accent hover:text-accent-foreground"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-2">
              {arrayValue.map((item: string, index: number) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={item}
                    onChange={(e) => updateArrayItem(fieldPath, index, e.target.value)}
                    placeholder={field.placeholder?.en}
                    className="bg-input border-border text-foreground placeholder:text-muted-foreground"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeArrayItem(fieldPath, index)}
                    className="h-10 w-10 p-0 flex-shrink-0 border-border hover:bg-destructive hover:text-destructive-foreground"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        );

      case 'object':
        return (
          <Card key={field.key} className="mt-4 bg-card border-border">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-card-foreground">{field.label.en}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {field.children?.map((childField) => renderField(childField, fieldPath))}
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      {template.schema.map((field) => renderField(field))}
    </div>
  );
}
